<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JSON数据对比与合并工具</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/multi-record-styles.css">
    <script src="./js/<EMAIL>" defer></script>
</head>
<body>
<div class="app-container" x-data="diffApp()" x-init="init()"
     @keydown.ctrl.a.prevent="currentMode === 'revision' ? acceptAllRevisions() : acceptAllChanges()"
     @keydown.meta.a.prevent="currentMode === 'revision' ? acceptAllRevisions() : acceptAllChanges()"
     @keydown.ctrl.r.prevent="currentMode === 'revision' ? rejectAllRevisions() : rejectAllChanges()"
     @keydown.meta.r.prevent="currentMode === 'revision' ? rejectAllRevisions() : rejectAllChanges()"
     @keydown.tab.prevent="switchMode(currentMode === 'diff' ? 'revision' : 'diff')">
    <!-- 应用头部 -->
    <div class="app-header">
        <div class="header-content">
            <div style="display: flex; align-items: center;">
                <h1 class="app-title">
                    <span>🔄</span>
                    
                    JSON数据对比与合并工具
                </h1>
                <span class="mode-indicator" :class="{ 'diff-mode': currentMode === 'diff' }">
                    <span>📝</span>
                    <span x-text="currentMode === 'revision' ? '修订模式' : '对比模式'"></span>
                </span>
            </div>
            <div class="version-control">
                <button class="btn btn-secondary" @click="showHistory()">
                    <span>📜</span>
                    操作历史
                </button>
                <button class="btn btn-secondary" @click="showVersionHistory()">
                    <span>📋</span>
                    版本历史
                </button>
                <button class="btn btn-secondary" @click="debugData()">
                    <span>🐛</span>
                    调试数据
                </button>
                <button class="btn btn-info" @click="exportResult()" x-show="currentMode === 'revision'">
                    <span>💾</span>
                    导出结果
                </button>
                <button class="btn btn-success" @click="currentMode === 'revision' ? acceptAllRevisions() : acceptAllChanges()">
                    <span>✓</span>
                    <span x-text="currentMode === 'revision' ? '接受所有修订' : '接受所有更改'"></span>
                </button>
                <button class="btn btn-danger" @click="currentMode === 'revision' ? rejectAllRevisions() : rejectAllChanges()">
                    <span>✗</span>
                    <span x-text="currentMode === 'revision' ? '拒绝所有修订' : '拒绝所有更改'"></span>
                </button>
            </div>
        </div>
    </div>

    <!-- 版本选择器 -->
    <div class="version-selector">
        <div class="version-tabs">
            <button class="version-tab" :class="{ active: currentMode === 'diff' }" @click="switchMode('diff')">
                对比模式 (版本1 ↔ 版本2)
            </button>
            <button class="version-tab" :class="{ active: currentMode === 'revision', 'has-data': targetData }" @click="switchMode('revision')">
                修订模式 (版本3)
            </button>
        </div>
        <div class="data-source-buttons">
            <button class="btn btn-primary" @click="showJsonInputModal()">
                <span>📝</span>
                输入JSON数据
            </button>
            <button class="btn btn-secondary" @click="loadSampleData()">
                <span>📁</span>
                加载示例数据
            </button>
            <button class="btn btn-secondary" @click="uploadFiles()">
                <span>⬆</span>
                上传文件
            </button>
        </div>
    </div>

    <!-- 状态栏 -->
    <div class="status-bar" x-show="currentMode === 'diff'">
        <div class="status-info">
            <div class="status-item">
                <div class="status-indicator status-added"></div>
                <span>新增: <span x-text="diffStats.added">0</span></span>
            </div>
            <div class="status-item">
                <div class="status-indicator status-removed"></div>
                <span>删除: <span x-text="diffStats.removed">0</span></span>
            </div>
            <div class="status-item">
                <div class="status-indicator status-modified"></div>
                <span>修改: <span x-text="diffStats.modified">0</span></span>
            </div>
        </div>
    </div>

    <!-- 修订摘要（仅在修订模式显示） -->
    <div class="revision-summary" x-show="currentMode === 'revision'">
        <div class="revision-summary-info">
            <div class="revision-summary-item">
                <div class="status-indicator status-revision"></div>
                <span>待处理修订: <span x-text="revisionStats.pending">0</span></span>
            </div>
            <div class="revision-summary-item">
                <div class="status-indicator status-added"></div>
                <span>已接受: <span x-text="revisionStats.accepted">0</span></span>
            </div>
            <div class="revision-summary-item">
                <div class="status-indicator status-removed"></div>
                <span>已拒绝: <span x-text="revisionStats.rejected">0</span></span>
            </div>
            <div class="revision-summary-item">
                <div class="status-indicator status-info"></div>
                <span>已是最新: <span x-text="revisionStats.upToDate">0</span></span>
            </div>
        </div>
        <div class="revision-summary-actions">
            <button class="btn btn-success" @click="acceptAllRevisions()">
                <span>✓</span>
                接受所有修订
            </button>
            <button class="btn btn-danger" @click="rejectAllRevisions()">
                <span>✗</span>
                拒绝所有修订
            </button>
        </div>
    </div>

    <!-- 过滤器 -->
    <div class="filter-section">
        <div class="filter-group" x-show="currentMode === 'diff'">
            <span class="filter-label">显示更改：</span>
            <div class="filter-buttons">
                <button class="filter-btn" :class="{ active: currentFilter === 'all' }" @click="setFilter('all')">
                    全部
                </button>
                <button class="filter-btn" :class="{ active: currentFilter === 'added' }" @click="setFilter('added')">
                    新增
                </button>
                <button class="filter-btn" :class="{ active: currentFilter === 'removed' }" @click="setFilter('removed')">
                    删除
                </button>
                <button class="filter-btn" :class="{ active: currentFilter === 'modified' }" @click="setFilter('modified')">
                    修改
                </button>
            </div>
        </div>
        <div class="filter-group" x-show="currentMode === 'revision'">
            <span class="filter-label">显示修订：</span>
            <div class="filter-buttons">
                <button class="revision-filter-btn" :class="{ active: revisionFilter === 'all' }" @click="setRevisionFilter('all')">
                    全部
                </button>
                <button class="revision-filter-btn" :class="{ active: revisionFilter === 'pending' }" @click="setRevisionFilter('pending')">
                    待处理
                </button>
                <button class="revision-filter-btn" :class="{ active: revisionFilter === 'accepted' }" @click="setRevisionFilter('accepted')">
                    已接受
                </button>
                <button class="revision-filter-btn" :class="{ active: revisionFilter === 'rejected' }" @click="setRevisionFilter('rejected')">
                    已拒绝
                </button>
                <button class="revision-filter-btn" :class="{ active: revisionFilter === 'uptodate' }" @click="setRevisionFilter('uptodate')">
                    已是最新
                </button>
            </div>
        </div>
        <div class="filter-group">
            <button class="btn btn-secondary" @click="expandAllModules()">展开全部</button>
            <button class="btn btn-secondary" @click="collapseAllModules()">折叠全部</button>
        </div>
    </div>

    <!-- 配置面板 -->
    <div class="config-panel" :class="{ collapsed: configPanelCollapsed }">
        <div class="config-panel-header" @click="toggleConfigPanel()">
            <div class="config-panel-title">
                <span class="config-panel-arrow" x-text="configPanelCollapsed ? '▼' : '▲'">▼</span>
                模块索引配置
            </div>
        </div>
        <div class="config-panel-body">
            <div x-html="moduleConfigsHtml"></div>
        </div>
    </div>

    <!-- 主内容区 -->
    <div class="main-content">
        <div class="content-header">
            <h2 class="content-title" x-text="currentMode === 'revision' ? '修订建议' : '对比结果'">对比结果</h2>
            <div>
                <span style="color: #6b7280; font-size: 14px;" x-text="currentMode === 'revision' ? '基于版本1→版本2的更改，对版本3的修订建议' : '版本1 → 版本2'">版本1 → 版本2</span>
            </div>
        </div>
        <div class="content-body" x-html="mainContentHtml">
            <div class="empty-state">
                <div class="empty-state-icon">📄</div>
                <div class="empty-state-text">请加载或输入JSON数据进行对比</div>
            </div>
        </div>
    </div>

    <!-- 快捷键提示 -->
    <div class="shortcuts">
        快捷键: <span class="shortcut-key">Ctrl+A</span> 接受全部 | <span class="shortcut-key">Ctrl+R</span> 拒绝全部 | <span class="shortcut-key">Tab</span> 切换模式
    </div>
</div>

<!-- JSON输入模态框 -->
<div class="modal" x-show="showJsonInputModal" @click.self="showJsonInputModal = false">
    <div class="modal-content">
        <div class="modal-header">
            <h2 class="modal-title">输入JSON数据</h2>
            <span class="modal-close" @click="showJsonInputModal = false">&times;</span>
        </div>
        <div class="json-input-container">
            <div class="json-input-section">
                <label class="json-input-label">
                    版本 1 JSON数据
                    <span class="version-badge">基准版本</span>
                </label>
                <textarea class="json-textarea" x-model="jsonInput1" placeholder="请粘贴第一个版本的JSON数据..."></textarea>
            </div>
            <div class="json-input-section">
                <label class="json-input-label">
                    版本 2 JSON数据
                    <span class="version-badge">对比版本</span>
                </label>
                <textarea class="json-textarea" x-model="jsonInput2" placeholder="请粘贴第二个版本的JSON数据..."></textarea>
            </div>
            <div class="json-input-section">
                <label class="json-input-label">
                    版本 3 JSON数据
                    <span class="version-badge primary">目标版本（可选）</span>
                </label>
                <textarea class="json-textarea" x-model="jsonInput3" placeholder="请粘贴第三个版本的JSON数据（用于修订）..."></textarea>
            </div>
        </div>
        <div class="json-input-actions">
            <button class="btn btn-secondary" @click="showJsonInputModal = false">取消</button>
            <button class="btn btn-primary" @click="parseJsonInput()">
                <span>✓</span>
                确认对比
            </button>
        </div>
    </div>
</div>

<!-- 通用模态框 -->
<div class="modal" x-show="showModal" @click.self="showModal = false">
    <div class="modal-content">
        <div class="modal-header">
            <h2 class="modal-title" x-text="modalTitle">标题</h2>
            <span class="modal-close" @click="showModal = false">&times;</span>
        </div>
        <div x-html="modalBody"></div>
    </div>
</div>

<!-- Toast提示 -->
<div class="toast" :class="`toast-${toastType}`" x-show="showToast" x-transition>
    <span class="toast-icon" x-text="toastIcon"></span>
    <span class="toast-message" x-text="toastMessage"></span>
</div>

<!-- 引入Alpine.js应用组件 -->
<script src="js/alpine-app.js"></script>
<!-- 引入必要的工具函数 -->
<script src="js/utils.js"></script>
<!-- 暂时保留的功能模块，后续会逐步迁移到Alpine.js -->
<script src="js/diff.js"></script>
<script src="js/revision.js"></script>
<script src="js/data-loader-js.js"></script>
<script src="js/multi-record-ui.js"></script>
</body>
</html>