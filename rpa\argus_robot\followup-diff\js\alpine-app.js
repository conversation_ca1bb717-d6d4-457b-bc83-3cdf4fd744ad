// Alpine.js 主应用组件 - 优化版本
function diffApp() {
    return {
        // 核心数据状态
        leftData: null,
        rightData: null,
        targetData: null,
        mergedData: null,

        // 模式和过滤器状态
        currentMode: 'diff',
        currentFilter: 'all',
        revisionFilter: 'all',

        // 统计数据
        diffStats: { added: 0, removed: 0, modified: 0 },
        revisionStats: { pending: 0, accepted: 0, rejected: 0, upToDate: 0 },

        // UI状态
        showJsonInputModal: false,
        showModal: false,
        showToast: false,
        configPanelCollapsed: true,
        isLoading: false,

        // 模态框和Toast数据
        modalTitle: '',
        modalBody: '',
        toastMessage: '',
        toastType: 'info',
        toastIcon: 'ℹ',

        // JSON输入数据
        jsonInput1: '',
        jsonInput2: '',
        jsonInput3: '',

        // 内容HTML
        mainContentHtml: `
            <div class="empty-state">
                <div class="empty-state-icon">📄</div>
                <div class="empty-state-text">请加载或输入JSON数据进行对比</div>
            </div>
        `,
        moduleConfigsHtml: '',

        // 性能优化：缓存
        _cache: new Map(),
        _debounceTimers: new Map(),
        
        // 存储管理
        storage: {
            versions: [],
            moduleIndexConfig: {
                '报告基础信息': [],
                '报告类型': [],
                '受试者信息': ['受试者筛选号', '受试者随机号'],
                'SAE/ECI/AESI的详细情况': ['不良事件名称', '发生日期'],
                '受试者死亡、住院信息': [],
                'SAE/ECI/AESI的描述': [],
                '其他导致该SAE/ECI/AESI的可能因素': ['事件名称'],
                '试验用药信息': {
                    '试验用药基础信息': [],
                    '试验用药列表信息': ['试验用药名称'],
                    '剂量信息': ['该剂量用药开始日期', '试验用药剂量数值']
                },
                '与事件相关的实验室检查': ['检查项目名称', '检查日期'],
                '与事件相关的现病史': ['疾病名称'],
                '与事件相关的既往病史': ['疾病名称'],
                '合并用药信息': ['合并用药品名称', '合并用药开始日期'],
                '治疗用药信息': ['治疗用药品名称', '治疗用药开始日期'],
                '报告者信息': [],
                '既往用药信息': ['既往用药品名称'],
                'AE': ['AE名称']
            },
            revisions: new Map(),
            acceptedChanges: new Map(),
            rejectedChanges: new Map(),
            operationHistory: [],
            dataVersion: 0
        },
        
        // 变更映射
        changeMap: new Map(),
        
        // 初始化方法
        init() {
            this.renderModuleConfigs();
            this.addToHistory({
                type: 'init',
                detail: '应用初始化完成'
            });

            // 性能优化：预加载常用功能
            this.preloadFeatures();
        },

        // 预加载功能
        preloadFeatures() {
            // 预编译常用的正则表达式
            this._regexCache = {
                jsonValidation: /^[\s]*[{\[]/,
                fieldPath: /^([^.]+)\.(.+)$/
            };

            // 预设置事件监听器
            this.setupGlobalEventListeners();
        },

        // 设置全局事件监听器
        setupGlobalEventListeners() {
            // 键盘快捷键
            document.addEventListener('keydown', (e) => {
                if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA') return;
                this.handleKeyboardShortcut(e);
            });

            // 防止意外关闭页面
            window.addEventListener('beforeunload', (e) => {
                if (this.hasUnsavedChanges()) {
                    e.preventDefault();
                    e.returnValue = '';
                }
            });
        },

        // 检查是否有未保存的更改
        hasUnsavedChanges() {
            return this.storage.acceptedChanges.size > 0 || this.storage.revisions.size > 0;
        },

        // 防抖函数
        debounce(key, fn, delay = 300) {
            if (this._debounceTimers.has(key)) {
                clearTimeout(this._debounceTimers.get(key));
            }

            const timer = setTimeout(() => {
                fn();
                this._debounceTimers.delete(key);
            }, delay);

            this._debounceTimers.set(key, timer);
        },

        // 缓存管理
        getFromCache(key) {
            return this._cache.get(key);
        },

        setToCache(key, value, ttl = 300000) { // 5分钟TTL
            this._cache.set(key, {
                value,
                expires: Date.now() + ttl
            });
        },

        clearExpiredCache() {
            const now = Date.now();
            for (const [key, item] of this._cache.entries()) {
                if (item.expires < now) {
                    this._cache.delete(key);
                }
            }
        },
        
        // 模式切换
        switchMode(mode) {
            this.currentMode = mode;
            this.addToHistory({
                type: 'mode_switch',
                mode: mode,
                detail: `切换到${mode === 'revision' ? '修订' : '对比'}模式`
            });
            
            // 重新渲染内容
            if (this.leftData && this.rightData) {
                if (mode === 'revision' && this.targetData) {
                    this.showRevisionMode();
                } else {
                    this.compareData();
                }
            }
        },
        
        // 显示JSON输入模态框
        openJsonInputModal() {
            // 预填充现有数据
            if (this.leftData) {
                this.jsonInput1 = JSON.stringify(this.leftData, null, 2);
            }
            if (this.rightData) {
                this.jsonInput2 = JSON.stringify(this.rightData, null, 2);
            }
            if (this.targetData) {
                this.jsonInput3 = JSON.stringify(this.targetData, null, 2);
            }
            this.showJsonInputModal = true;
        },
        
        // 解析JSON输入
        parseJsonInput() {
            const json1 = this.jsonInput1.trim();
            const json2 = this.jsonInput2.trim();
            const json3 = this.jsonInput3.trim();
            
            if (!json1 || !json2) {
                this.showToastMessage('请至少输入版本1和版本2的JSON数据', 'warning');
                return;
            }
            
            try {
                this.leftData = JSON.parse(json1);
                this.rightData = JSON.parse(json2);
                
                // 清空修订状态
                this.clearRevisions();
                this.clearChangeStatus();
                this.storage.dataVersion++;
                
                // 保存版本
                this.addVersion({
                    left: this.leftData,
                    right: this.rightData,
                    target: this.targetData
                });
                
                // 记录操作历史
                this.addToHistory({
                    type: 'data_input',
                    detail: '输入JSON数据'
                });
                
                if (json3) {
                    this.targetData = JSON.parse(json3);
                    console.log('版本3数据已加载:', Object.keys(this.targetData));
                } else {
                    this.targetData = null;
                }
                
                this.showJsonInputModal = false;
                
                // 如果有版本3且当前是修订模式，显示修订
                if (this.targetData && this.currentMode === 'revision') {
                    this.showRevisionMode();
                } else {
                    this.compareData();
                }
                
                this.showToastMessage('JSON数据解析成功', 'success');
                
                // 自动切换到修订模式（如果有版本3）
                if (this.targetData && this.currentMode !== 'revision') {
                    this.showToastMessage('检测到版本3数据，已自动切换到修订模式', 'info');
                    this.switchMode('revision');
                }
            } catch (error) {
                this.showToastMessage('JSON解析失败: ' + error.message, 'error');
            }
        },
        
        // Toast提示方法
        showToastMessage(message, type = 'info') {
            this.toastMessage = message;
            this.toastType = type;
            this.toastIcon = type === 'success' ? '✓' : 
                           type === 'error' ? '✗' : 
                           type === 'warning' ? '!' : 'ℹ';
            this.showToast = true;
            
            setTimeout(() => {
                this.showToast = false;
            }, 3000);
        },
        
        // 显示模态框
        showModalDialog(title, content) {
            this.modalTitle = title;
            this.modalBody = content;
            this.showModal = true;
        },
        
        // 历史记录管理
        addToHistory(entry) {
            entry.timestamp = new Date().toISOString();
            this.storage.operationHistory.push(entry);
            // 保留最近50条记录
            if (this.storage.operationHistory.length > 50) {
                this.storage.operationHistory.shift();
            }
        },
        
        // 版本管理
        addVersion(data) {
            const version = {
                id: Date.now(),
                timestamp: new Date().toISOString(),
                data: JSON.parse(JSON.stringify(data)),
                name: `版本 ${this.storage.versions.length + 1}`
            };
            this.storage.versions.push(version);
            // 保留最近10个版本
            if (this.storage.versions.length > 10) {
                this.storage.versions.shift();
            }
            return version;
        },
        
        // 清理方法
        clearRevisions() {
            this.storage.revisions.clear();
            this.updateRevisionStats();
        },
        
        clearChangeStatus() {
            this.storage.acceptedChanges.clear();
            this.storage.rejectedChanges.clear();
        },
        
        // 统计更新
        updateRevisionStats() {
            let pending = 0, accepted = 0, rejected = 0, upToDate = 0;
            
            this.storage.revisions.forEach(status => {
                switch (status) {
                    case 'pending': pending++; break;
                    case 'accepted': accepted++; break;
                    case 'rejected': rejected++; break;
                    case 'uptodate': upToDate++; break;
                }
            });
            
            this.revisionStats = { pending, accepted, rejected, upToDate };
        },
        
        // 配置面板
        toggleConfigPanel() {
            this.configPanelCollapsed = !this.configPanelCollapsed;
        },
        
        renderModuleConfigs() {
            const configs = this.storage.moduleIndexConfig;
            let html = '';

            for (const [moduleName, fields] of Object.entries(configs)) {
                const fieldsHtml = Array.isArray(fields) ? fields.map(field => `
                    <span class="tag">
                        ${field}
                        <span class="tag-remove" @click="removeModuleIndexField('${moduleName}', '${field}')">&times;</span>
                    </span>
                `).join('') : '';

                html += `
                    <div class="module-config" data-module="${moduleName}">
                        <div class="module-config-header" @click="toggleModuleConfig($event.target)">
                            <span>${moduleName}</span>
                            <span class="module-config-arrow">▼</span>
                        </div>
                        <div class="module-config-body" style="display: none;">
                            <div class="tag-input">
                                <div class="tags">
                                    ${fieldsHtml}
                                </div>
                                <input type="text" placeholder="输入索引字段名称..."
                                       @keydown.enter="addModuleIndexField('${moduleName}')">
                                <button class="btn btn-sm btn-primary" @click="addModuleIndexField('${moduleName}')">
                                    添加
                                </button>
                            </div>
                            <div class="module-config-help">
                                <small>索引字段用于智能匹配数组中的项目，如"受试者筛选号"、"不良事件名称"等</small>
                            </div>
                        </div>
                    </div>
                `;
            }

            this.moduleConfigsHtml = html || '<div class="empty-config">暂无模块配置</div>';
        },

        // 数据对比功能 - 优化版本
        compareData() {
            if (!this.leftData || !this.rightData) return;

            // 生成缓存键
            const cacheKey = `compare_${this.generateDataHash(this.leftData)}_${this.generateDataHash(this.rightData)}`;

            // 检查缓存
            const cached = this.getFromCache(cacheKey);
            if (cached && cached.expires > Date.now()) {
                this.diffStats = cached.value.stats;
                this.mainContentHtml = cached.value.html;
                this.showToastMessage('使用缓存结果，提升性能', 'info');
                return;
            }

            try {
                this.isLoading = true;

                this.measurePerformance('数据对比', () => {
                    this.diffStats = { added: 0, removed: 0, modified: 0 };
                    this.changeMap.clear();

                    // 清理数据
                    const cleanedLeftData = this.cleanObject(this.leftData);
                    const cleanedRightData = this.cleanObject(this.rightData);

                    // 按模块组织数据
                    const leftModules = this.organizeByModules(cleanedLeftData);
                    const rightModules = this.organizeByModules(cleanedRightData);

                    // 渲染合并的对比结果
                    const html = this.renderMergedComparison(leftModules, rightModules);

                    // 缓存结果
                    this.setToCache(cacheKey, {
                        stats: { ...this.diffStats },
                        html: html
                    });

                    // 同步到全局变量（兼容性）
                    window.diffStats = { ...this.diffStats };
                    window.leftData = this.leftData;
                    window.rightData = this.rightData;
                });
            } catch (error) {
                this.handleError(error, '数据对比');
            } finally {
                this.isLoading = false;
            }
        },

        // 生成数据哈希用于缓存
        generateDataHash(data) {
            return btoa(JSON.stringify(data)).slice(0, 16);
        },

        // 清理对象数据
        cleanObject(obj) {
            if (!obj || typeof obj !== 'object') return obj;

            const cleaned = {};
            for (const [key, value] of Object.entries(obj)) {
                if (key !== undefined && key !== 'undefined' && value !== undefined) {
                    if (typeof value === 'object' && value !== null) {
                        cleaned[key] = this.cleanObject(value);
                    } else {
                        cleaned[key] = value;
                    }
                }
            }
            return cleaned;
        },

        // 按模块组织数据
        organizeByModules(data) {
            const modules = {};

            for (const [key, value] of Object.entries(data)) {
                if (typeof value === 'object' && value !== null && value['报告模块']) {
                    const moduleName = value['报告模块'];
                    if (!modules[moduleName]) {
                        modules[moduleName] = {};
                    }
                    modules[moduleName][key] = value;
                } else {
                    // 如果没有报告模块字段，使用键名作为模块名
                    modules[key] = value;
                }
            }

            return modules;
        },

        // 渲染合并的对比结果
        renderMergedComparison(leftModules, rightModules) {
            let html = '';

            // 获取所有模块名
            const allModules = new Set([...Object.keys(leftModules), ...Object.keys(rightModules)]);

            for (const moduleName of allModules) {
                const module1Data = leftModules[moduleName] || {};
                const module2Data = rightModules[moduleName] || {};

                const moduleStats = { added: 0, removed: 0, modified: 0 };

                // 直接使用模块数据进行对比
                const comparison = this.compareModuleData(module1Data, module2Data, moduleName, moduleStats);

                if (comparison && Object.keys(comparison).length > 0) {
                    html += this.renderMergedModule(moduleName, comparison, moduleStats);
                }
            }

            this.mainContentHtml = html || '<div class="empty-state"><div class="empty-state-icon">📄</div><div class="empty-state-text">无数据差异</div></div>';

            // 应用当前过滤器
            this.$nextTick(() => {
                this.applyFilter();
            });
        },

        // 对比模块数据
        compareModuleData(data1, data2, moduleName, stats) {
            const result = {};
            const allKeys = new Set([...Object.keys(data1), ...Object.keys(data2)]);

            for (const key of allKeys) {
                // 跳过undefined的键
                if (key === undefined || key === 'undefined') continue;

                const value1 = data1[key];
                const value2 = data2[key];

                if (value1 === undefined) {
                    stats.added++;
                    this.diffStats.added++;
                    result[key] = { status: 'added', value: value2 };
                } else if (value2 === undefined) {
                    stats.removed++;
                    this.diffStats.removed++;
                    result[key] = { status: 'removed', value: value1 };
                } else if (Array.isArray(value1) && Array.isArray(value2)) {
                    // 数组对比
                    result[key] = this.compareArrayData(value1, value2, moduleName, stats);
                } else if (typeof value1 === 'object' && typeof value2 === 'object') {
                    // 对象对比
                    result[key] = this.compareObjectData(value1, value2, stats);
                } else if (JSON.stringify(value1) !== JSON.stringify(value2)) {
                    stats.modified++;
                    this.diffStats.modified++;
                    result[key] = { status: 'modified', value: value2, oldValue: value1 };
                } else {
                    result[key] = { status: 'unchanged', value: value1 };
                }
            }

            return result;
        },

        // 对比数组数据
        compareArrayData(arr1, arr2, moduleName, stats) {
            const indexFields = this.getIndexFieldsForModule(moduleName);
            const matched = new Map();
            const result = [];

            // 根据索引字段匹配记录
            if (indexFields.length > 0) {
                // 建立索引
                const index2Map = new Map();
                arr2.forEach((item, idx) => {
                    const keys = indexFields.map(field => item[field]).filter(v => v).join('|');
                    if (keys) {
                        index2Map.set(keys, idx);
                    }
                });

                // 匹配记录
                arr1.forEach((item1, idx1) => {
                    const keys = indexFields.map(field => item1[field]).filter(v => v).join('|');
                    if (keys && index2Map.has(keys)) {
                        const idx2 = index2Map.get(keys);
                        matched.set(idx2, true);
                        const comparison = this.compareObjectData(item1, arr2[idx2], stats);
                        result.push({ ...comparison, index: idx1, matchedIndex: idx2 });
                    } else {
                        stats.removed++;
                        this.diffStats.removed++;
                        result.push({ status: 'removed', value: item1, index: idx1 });
                    }
                });

                // 添加新增的记录
                arr2.forEach((item2, idx2) => {
                    if (!matched.has(idx2)) {
                        stats.added++;
                        this.diffStats.added++;
                        result.push({ status: 'added', value: item2, index: idx2 });
                    }
                });
            } else {
                // 简单的位置对比
                const maxLength = Math.max(arr1.length, arr2.length);
                for (let i = 0; i < maxLength; i++) {
                    if (i >= arr1.length) {
                        stats.added++;
                        this.diffStats.added++;
                        result.push({ status: 'added', value: arr2[i], index: i });
                    } else if (i >= arr2.length) {
                        stats.removed++;
                        this.diffStats.removed++;
                        result.push({ status: 'removed', value: arr1[i], index: i });
                    } else {
                        const comparison = this.compareObjectData(arr1[i], arr2[i], stats);
                        result.push({ ...comparison, index: i });
                    }
                }
            }

            return { isArray: true, items: result };
        },

        // 对比对象数据
        compareObjectData(obj1, obj2, stats) {
            const result = {};
            const allKeys = new Set([...Object.keys(obj1), ...Object.keys(obj2)]);

            for (const key of allKeys) {
                const value1 = obj1[key];
                const value2 = obj2[key];

                if (value1 === undefined) {
                    stats.added++;
                    this.diffStats.added++;
                    result[key] = { status: 'added', value: value2 };
                } else if (value2 === undefined) {
                    stats.removed++;
                    this.diffStats.removed++;
                    result[key] = { status: 'removed', value: value1 };
                } else if (JSON.stringify(value1) !== JSON.stringify(value2)) {
                    stats.modified++;
                    this.diffStats.modified++;
                    result[key] = { status: 'modified', value: value2, oldValue: value1 };
                } else {
                    result[key] = { status: 'unchanged', value: value1 };
                }
            }

            return result;
        },

        // 获取模块的索引字段
        getIndexFieldsForModule(moduleName) {
            const config = this.storage.moduleIndexConfig[moduleName];
            if (Array.isArray(config)) {
                return config;
            } else if (typeof config === 'object' && config !== null) {
                // 处理嵌套配置
                return Object.values(config).flat();
            }
            return [];
        },

        // 渲染合并模块
        renderMergedModule(moduleName, comparison, stats) {
            const hasChanges = stats.added > 0 || stats.removed > 0 || stats.modified > 0;

            if (!hasChanges && this.currentFilter !== 'all') {
                return '';
            }

            let html = `
                <div class="module-section" data-module="${moduleName}">
                    <div class="module-header" onclick="toggleModule(this)">
                        <div class="module-title">
                            <span class="module-icon">📋</span>
                            <span class="module-name">${moduleName}</span>
                            <span class="module-stats">
                                ${stats.added > 0 ? `<span class="stat-badge stat-added">+${stats.added}</span>` : ''}
                                ${stats.removed > 0 ? `<span class="stat-badge stat-removed">-${stats.removed}</span>` : ''}
                                ${stats.modified > 0 ? `<span class="stat-badge stat-modified">~${stats.modified}</span>` : ''}
                            </span>
                        </div>
                        <span class="module-arrow">▼</span>
                    </div>
                    <div class="module-body">
            `;

            for (const [key, value] of Object.entries(comparison)) {
                if (value.isArray) {
                    html += this.renderArrayComparison(key, value);
                } else {
                    html += this.renderFieldComparison(key, value);
                }
            }

            html += `
                    </div>
                </div>
            `;

            return html;
        },

        // 渲染字段对比
        renderFieldComparison(fieldName, comparison) {
            const status = comparison.status;

            if (this.currentFilter !== 'all' && this.currentFilter !== status) {
                return '';
            }

            let html = `
                <div class="field-row field-${status}" data-status="${status}">
                    <div class="field-label">${fieldName}</div>
                    <div class="field-comparison">
            `;

            switch (status) {
                case 'added':
                    html += `
                        <div class="field-value field-value-new">
                            <div class="value-label">新增</div>
                            <div class="value-content">${this.formatValue(comparison.value)}</div>
                        </div>
                    `;
                    break;

                case 'removed':
                    html += `
                        <div class="field-value field-value-old">
                            <div class="value-label">删除</div>
                            <div class="value-content">${this.formatValue(comparison.value)}</div>
                        </div>
                    `;
                    break;

                case 'modified':
                    html += `
                        <div class="field-value field-value-old">
                            <div class="value-label">原值</div>
                            <div class="value-content">${this.formatValue(comparison.oldValue)}</div>
                        </div>
                        <div class="field-arrow">→</div>
                        <div class="field-value field-value-new">
                            <div class="value-label">新值</div>
                            <div class="value-content">${this.formatValue(comparison.value)}</div>
                        </div>
                    `;
                    break;

                case 'unchanged':
                    if (this.currentFilter === 'all') {
                        html += `
                            <div class="field-value field-value-unchanged">
                                <div class="value-label">无变化</div>
                                <div class="value-content">${this.formatValue(comparison.value)}</div>
                            </div>
                        `;
                    }
                    break;
            }

            html += `
                    </div>
                    <div class="field-actions">
                        ${status !== 'unchanged' ? `
                            <button class="btn btn-sm btn-success" onclick="acceptChange('${fieldName}')">
                                <span>✓</span>
                            </button>
                            <button class="btn btn-sm btn-danger" onclick="rejectChange('${fieldName}')">
                                <span>✗</span>
                            </button>
                        ` : ''}
                    </div>
                </div>
            `;

            return html;
        },

        // 渲染数组对比
        renderArrayComparison(fieldName, arrayComparison) {
            let html = `
                <div class="array-section" data-field="${fieldName}">
                    <div class="array-header">
                        <span class="array-name">${fieldName}</span>
                        <span class="array-count">${arrayComparison.items.length} 项</span>
                    </div>
                    <div class="array-items">
            `;

            arrayComparison.items.forEach((item, index) => {
                html += this.renderArrayItem(item, index);
            });

            html += `
                    </div>
                </div>
            `;

            return html;
        },

        // 渲染数组项
        renderArrayItem(item, index) {
            const status = item.status || 'unchanged';

            if (this.currentFilter !== 'all' && this.currentFilter !== status) {
                return '';
            }

            let html = `
                <div class="array-item array-item-${status}" data-status="${status}" data-index="${index}">
                    <div class="array-item-header">
                        <span class="array-item-index">#${index + 1}</span>
                        <span class="array-item-status status-${status}">${this.getStatusText(status)}</span>
                    </div>
                    <div class="array-item-content">
            `;

            if (typeof item.value === 'object') {
                for (const [key, value] of Object.entries(item.value)) {
                    html += `
                        <div class="array-field">
                            <span class="array-field-name">${key}:</span>
                            <span class="array-field-value">${this.formatValue(value)}</span>
                        </div>
                    `;
                }
            } else {
                html += `<div class="array-simple-value">${this.formatValue(item.value)}</div>`;
            }

            html += `
                    </div>
                </div>
            `;

            return html;
        },

        // 格式化值显示
        formatValue(value) {
            if (value === null || value === undefined) {
                return '<span class="null-value">null</span>';
            }

            if (typeof value === 'string') {
                return value.length > 100 ? value.substring(0, 100) + '...' : value;
            }

            if (typeof value === 'object') {
                return `<pre class="json-value">${JSON.stringify(value, null, 2)}</pre>`;
            }

            return String(value);
        },

        // 获取状态文本
        getStatusText(status) {
            const statusMap = {
                'added': '新增',
                'removed': '删除',
                'modified': '修改',
                'unchanged': '无变化'
            };
            return statusMap[status] || status;
        },

        // 接受单个更改
        acceptChange(fieldPath) {
            this.storage.acceptedChanges.set(fieldPath, true);
            this.storage.rejectedChanges.delete(fieldPath);

            // 更新UI显示
            const element = document.querySelector(`[data-field-path="${fieldPath}"]`);
            if (element) {
                element.classList.add('change-accepted');
                element.classList.remove('change-rejected');
            }

            this.addToHistory({
                type: 'accept_change',
                detail: `接受更改: ${fieldPath}`
            });

            this.showToastMessage(`已接受更改: ${fieldPath}`, 'success');
        },

        // 拒绝单个更改
        rejectChange(fieldPath) {
            this.storage.rejectedChanges.set(fieldPath, true);
            this.storage.acceptedChanges.delete(fieldPath);

            // 更新UI显示
            const element = document.querySelector(`[data-field-path="${fieldPath}"]`);
            if (element) {
                element.classList.add('change-rejected');
                element.classList.remove('change-accepted');
            }

            this.addToHistory({
                type: 'reject_change',
                detail: `拒绝更改: ${fieldPath}`
            });

            this.showToastMessage(`已拒绝更改: ${fieldPath}`, 'warning');
        },

        // 切换模块展开/折叠
        toggleModule(headerElement) {
            const moduleBody = headerElement.nextElementSibling;
            const arrow = headerElement.querySelector('.module-arrow');

            if (moduleBody.style.display === 'none') {
                moduleBody.style.display = 'block';
                arrow.textContent = '▲';
            } else {
                moduleBody.style.display = 'none';
                arrow.textContent = '▼';
            }
        },

        // 展开所有模块
        expandAllModules() {
            const modules = document.querySelectorAll('.module-body');
            const arrows = document.querySelectorAll('.module-arrow');

            modules.forEach(module => {
                module.style.display = 'block';
            });

            arrows.forEach(arrow => {
                arrow.textContent = '▲';
            });

            this.showToastMessage('已展开所有模块', 'info');
        },

        // 折叠所有模块
        collapseAllModules() {
            const modules = document.querySelectorAll('.module-body');
            const arrows = document.querySelectorAll('.module-arrow');

            modules.forEach(module => {
                module.style.display = 'none';
            });

            arrows.forEach(arrow => {
                arrow.textContent = '▼';
            });

            this.showToastMessage('已折叠所有模块', 'info');
        },

        // 应用过滤器
        applyFilter() {
            const elements = document.querySelectorAll('.field-row, .array-item');

            elements.forEach(element => {
                const status = element.dataset.status;
                const shouldShow = this.currentFilter === 'all' || this.currentFilter === status;
                element.style.display = shouldShow ? 'block' : 'none';
            });

            // 隐藏没有可见子项的模块
            const modules = document.querySelectorAll('.module-section');
            modules.forEach(module => {
                const visibleItems = module.querySelectorAll('.field-row:not([style*="display: none"]), .array-item:not([style*="display: none"])');
                const moduleElement = module;
                moduleElement.style.display = visibleItems.length > 0 ? 'block' : 'none';
            });
        },

        // 搜索内容 - 优化版本
        searchInContent(searchTerm) {
            this.debounce('search', () => {
                this._performSearch(searchTerm);
            }, 300);
        },

        // 执行搜索
        _performSearch(searchTerm) {
            if (!searchTerm.trim()) {
                this.applyFilter();
                return;
            }

            const term = searchTerm.toLowerCase();
            const cacheKey = `search_${term}`;

            // 检查搜索缓存
            const cached = this.getFromCache(cacheKey);
            if (cached && cached.expires > Date.now()) {
                this._applySearchResults(cached.value);
                return;
            }

            // 执行搜索
            const elements = document.querySelectorAll('.field-row, .array-item, .module-section');
            const results = [];

            elements.forEach((element, index) => {
                const text = element.textContent.toLowerCase();
                const shouldShow = text.includes(term);
                results.push({ index, shouldShow });
                element.style.display = shouldShow ? 'block' : 'none';
            });

            // 缓存搜索结果
            this.setToCache(cacheKey, results, 60000); // 1分钟缓存
        },

        // 应用搜索结果
        _applySearchResults(results) {
            const elements = document.querySelectorAll('.field-row, .array-item, .module-section');
            results.forEach(result => {
                if (elements[result.index]) {
                    elements[result.index].style.display = result.shouldShow ? 'block' : 'none';
                }
            });
        },

        // 获取对比统计
        getComparisonStats() {
            return {
                total: this.diffStats.added + this.diffStats.removed + this.diffStats.modified,
                ...this.diffStats
            };
        },

        // 重置对比状态
        resetComparison() {
            this.diffStats = { added: 0, removed: 0, modified: 0 };
            this.changeMap.clear();
            this.storage.acceptedChanges.clear();
            this.storage.rejectedChanges.clear();
            this.mainContentHtml = `
                <div class="empty-state">
                    <div class="empty-state-icon">📄</div>
                    <div class="empty-state-text">请加载或输入JSON数据进行对比</div>
                </div>
            `;
        },

        // 修订模式
        showRevisionMode() {
            if (!this.leftData || !this.rightData || !this.targetData) {
                this.showToastMessage('需要所有三个版本的数据才能进入修订模式', 'warning');
                return;
            }

            try {
                this.measurePerformance('修订模式生成', () => {
                    // 检查是否有任何接受的更改
                    if (this.storage.acceptedChanges.size === 0) {
                        this.showToastMessage('提示：请先在对比模式下接受需要的更改，然后再进入修订模式', 'info');
                    }

                    this.revisionStats = { pending: 0, accepted: 0, rejected: 0, upToDate: 0 };

                    // 清理数据
                    const cleanedLeftData = this.cleanObject(this.leftData);
                    const cleanedRightData = this.cleanObject(this.rightData);
                    const cleanedTargetData = this.cleanObject(this.targetData);

                    // 按模块组织数据
                    const leftModules = this.organizeByModules(cleanedLeftData);
                    const rightModules = this.organizeByModules(cleanedRightData);
                    const targetModules = this.organizeByModules(cleanedTargetData);

                    // 渲染修订模式
                    this.renderRevisionMode(leftModules, rightModules, targetModules);

                    // 同步到全局变量（兼容性）
                    window.revisionStats = { ...this.revisionStats };
                });
            } catch (error) {
                this.handleError(error, '修订模式');
            }
        },

        // 渲染修订模式
        renderRevisionMode(leftModules, rightModules, targetModules) {
            let html = '';
            let hasAnyRevisions = false;
            let totalRevisions = 0;

            // 重置修订统计
            this.revisionStats = { pending: 0, accepted: 0, rejected: 0, upToDate: 0 };

            // 获取所有模块名
            const allModules = new Set([...Object.keys(leftModules), ...Object.keys(rightModules), ...Object.keys(targetModules)]);

            console.log('开始生成修订建议，模块数:', allModules.size);

            for (const moduleName of allModules) {
                const module1Data = leftModules[moduleName] || {};
                const module2Data = rightModules[moduleName] || {};
                const module3Data = targetModules[moduleName] || {};

                const revisions = this.generateRevisions(module1Data, module2Data, module3Data, moduleName);

                if (revisions && revisions.length > 0) {
                    hasAnyRevisions = true;
                    totalRevisions += revisions.length;
                    html += this.renderRevisionModule(moduleName, revisions);
                    console.log(`模块 ${moduleName} 生成了 ${revisions.length} 个修订建议`);
                }
            }

            if (!hasAnyRevisions) {
                console.log('没有生成任何修订建议');
                html = `
                    <div class="empty-state">
                        <div class="empty-state-icon">✅</div>
                        <div class="empty-state-text">
                            <h3>版本3已是最新</h3>
                            <p>基于您在对比模式下接受的更改，版本3的数据已经是最新的，无需修订。</p>
                        </div>
                    </div>
                `;
            } else {
                console.log(`总共生成了 ${totalRevisions} 个修订建议`);
            }

            this.mainContentHtml = html;

            // 应用修订过滤器
            this.$nextTick(() => {
                this.applyRevisionFilter();
            });
        },

        // 生成修订建议
        generateRevisions(module1Data, module2Data, module3Data, moduleName) {
            const revisions = [];
            const acceptedChanges = this.storage.acceptedChanges;

            // 获取所有字段
            const allFields = new Set([...Object.keys(module1Data), ...Object.keys(module2Data), ...Object.keys(module3Data)]);

            for (const fieldName of allFields) {
                const value1 = module1Data[fieldName];
                const value2 = module2Data[fieldName];
                const value3 = module3Data[fieldName];

                // 构建字段路径用于检查是否被接受
                const fieldPath = `${moduleName}.${fieldName}`;

                // 检查这个字段的更改是否被接受
                const isAccepted = acceptedChanges.has(fieldPath);

                if (isAccepted) {
                    // 如果更改被接受，检查版本3是否需要更新
                    const revision = this.createRevisionSuggestion(fieldName, value1, value2, value3, fieldPath);
                    if (revision) {
                        revisions.push(revision);
                    }
                }
            }

            return revisions;
        },

        // 创建修订建议
        createRevisionSuggestion(fieldName, value1, value2, value3, fieldPath) {
            // 比较值是否相等
            const value1Str = JSON.stringify(value1);
            const value2Str = JSON.stringify(value2);
            const value3Str = JSON.stringify(value3);

            let revisionType = '';
            let suggestion = null;

            if (value1 === undefined && value2 !== undefined) {
                // 新增字段
                if (value3 === undefined) {
                    revisionType = 'add';
                    suggestion = {
                        action: 'add',
                        newValue: value2,
                        reason: '版本2中新增了此字段'
                    };
                } else if (value3Str === value2Str) {
                    revisionType = 'uptodate';
                    suggestion = {
                        action: 'none',
                        reason: '版本3已包含此字段且值正确'
                    };
                } else {
                    revisionType = 'update';
                    suggestion = {
                        action: 'update',
                        oldValue: value3,
                        newValue: value2,
                        reason: '版本3中此字段值需要更新'
                    };
                }
            } else if (value1 !== undefined && value2 === undefined) {
                // 删除字段
                if (value3 !== undefined) {
                    revisionType = 'remove';
                    suggestion = {
                        action: 'remove',
                        oldValue: value3,
                        reason: '版本2中删除了此字段'
                    };
                } else {
                    revisionType = 'uptodate';
                    suggestion = {
                        action: 'none',
                        reason: '版本3已正确删除此字段'
                    };
                }
            } else if (value1Str !== value2Str) {
                // 修改字段
                if (value3Str === value2Str) {
                    revisionType = 'uptodate';
                    suggestion = {
                        action: 'none',
                        reason: '版本3已更新到正确值'
                    };
                } else {
                    revisionType = 'update';
                    suggestion = {
                        action: 'update',
                        oldValue: value3,
                        newValue: value2,
                        reason: '版本3中此字段值需要更新'
                    };
                }
            } else {
                // 无变化
                return null;
            }

            // 更新统计
            switch (revisionType) {
                case 'add':
                case 'update':
                case 'remove':
                    this.revisionStats.pending++;
                    break;
                case 'uptodate':
                    this.revisionStats.upToDate++;
                    break;
            }

            return {
                fieldName,
                fieldPath,
                type: revisionType,
                suggestion,
                status: 'pending' // pending, accepted, rejected
            };
        },

        // 渲染修订模块
        renderRevisionModule(moduleName, revisions) {
            let html = `
                <div class="revision-module" data-module="${moduleName}">
                    <div class="revision-module-header" onclick="toggleRevisionModule(this)">
                        <div class="revision-module-title">
                            <span class="module-icon">📋</span>
                            <span class="module-name">${moduleName}</span>
                            <span class="revision-count">${revisions.length} 个修订建议</span>
                        </div>
                        <span class="module-arrow">▼</span>
                    </div>
                    <div class="revision-module-body">
            `;

            revisions.forEach(revision => {
                html += this.renderRevisionItem(revision);
            });

            html += `
                    </div>
                </div>
            `;

            return html;
        },

        // 渲染修订项
        renderRevisionItem(revision) {
            const { fieldName, fieldPath, type, suggestion, status } = revision;

            let statusText = this.getRevisionStatusText(type);
            let actionButtons = '';

            if (type !== 'uptodate') {
                actionButtons = `
                    <div class="revision-actions">
                        <button class="btn btn-sm btn-success" onclick="acceptRevision('${fieldPath}')"
                                ${status === 'accepted' ? 'disabled' : ''}>
                            <span>✓</span> 接受
                        </button>
                        <button class="btn btn-sm btn-danger" onclick="rejectRevision('${fieldPath}')"
                                ${status === 'rejected' ? 'disabled' : ''}>
                            <span>✗</span> 拒绝
                        </button>
                        ${status !== 'pending' ? `
                            <button class="btn btn-sm btn-secondary" onclick="undoRevision('${fieldPath}')">
                                <span>↶</span> 撤销
                            </button>
                        ` : ''}
                    </div>
                `;
            }

            let html = `
                <div class="revision-item revision-item-${type} revision-status-${status}"
                     data-revision-type="${type}" data-status="${status}" data-field-path="${fieldPath}">
                    <div class="revision-header">
                        <div class="revision-field-name">${fieldName}</div>
                        <div class="revision-status-badge status-${type}">${statusText}</div>
                    </div>
                    <div class="revision-content">
                        <div class="revision-description">${suggestion.reason}</div>
            `;

            // 根据操作类型显示不同的内容
            switch (suggestion.action) {
                case 'add':
                    html += `
                        <div class="revision-change">
                            <div class="revision-change-label">建议添加:</div>
                            <div class="revision-value revision-value-new">${this.formatValue(suggestion.newValue)}</div>
                        </div>
                    `;
                    break;

                case 'update':
                    html += `
                        <div class="revision-change">
                            <div class="revision-change-label">当前值:</div>
                            <div class="revision-value revision-value-old">${this.formatValue(suggestion.oldValue)}</div>
                            <div class="revision-arrow">→</div>
                            <div class="revision-change-label">建议值:</div>
                            <div class="revision-value revision-value-new">${this.formatValue(suggestion.newValue)}</div>
                        </div>
                    `;
                    break;

                case 'remove':
                    html += `
                        <div class="revision-change">
                            <div class="revision-change-label">建议删除:</div>
                            <div class="revision-value revision-value-old">${this.formatValue(suggestion.oldValue)}</div>
                        </div>
                    `;
                    break;

                case 'none':
                    html += `
                        <div class="revision-change">
                            <div class="revision-no-action">无需操作</div>
                        </div>
                    `;
                    break;
            }

            html += `
                    </div>
                    ${actionButtons}
                </div>
            `;

            return html;
        },

        // 获取修订状态文本
        getRevisionStatusText(type) {
            const statusMap = {
                'add': '建议新增',
                'update': '建议更新',
                'remove': '建议删除',
                'uptodate': '已是最新'
            };
            return statusMap[type] || type;
        },

        // 接受修订
        acceptRevision(fieldPath) {
            this.storage.revisions.set(fieldPath, 'accepted');

            // 更新UI
            const element = document.querySelector(`[data-field-path="${fieldPath}"]`);
            if (element) {
                element.classList.remove('revision-status-pending', 'revision-status-rejected');
                element.classList.add('revision-status-accepted');

                // 禁用接受按钮，启用撤销按钮
                const acceptBtn = element.querySelector('.btn-success');
                const rejectBtn = element.querySelector('.btn-danger');
                if (acceptBtn) acceptBtn.disabled = true;
                if (rejectBtn) rejectBtn.disabled = false;
            }

            this.updateRevisionStats();
            this.addToHistory({
                type: 'accept_revision',
                detail: `接受修订: ${fieldPath}`
            });

            this.showToastMessage(`已接受修订: ${fieldPath}`, 'success');
        },

        // 拒绝修订
        rejectRevision(fieldPath) {
            this.storage.revisions.set(fieldPath, 'rejected');

            // 更新UI
            const element = document.querySelector(`[data-field-path="${fieldPath}"]`);
            if (element) {
                element.classList.remove('revision-status-pending', 'revision-status-accepted');
                element.classList.add('revision-status-rejected');

                // 禁用拒绝按钮，启用撤销按钮
                const acceptBtn = element.querySelector('.btn-success');
                const rejectBtn = element.querySelector('.btn-danger');
                if (acceptBtn) acceptBtn.disabled = false;
                if (rejectBtn) rejectBtn.disabled = true;
            }

            this.updateRevisionStats();
            this.addToHistory({
                type: 'reject_revision',
                detail: `拒绝修订: ${fieldPath}`
            });

            this.showToastMessage(`已拒绝修订: ${fieldPath}`, 'warning');
        },

        // 撤销修订操作
        undoRevision(fieldPath) {
            this.storage.revisions.set(fieldPath, 'pending');

            // 更新UI
            const element = document.querySelector(`[data-field-path="${fieldPath}"]`);
            if (element) {
                element.classList.remove('revision-status-accepted', 'revision-status-rejected');
                element.classList.add('revision-status-pending');

                // 启用所有按钮
                const acceptBtn = element.querySelector('.btn-success');
                const rejectBtn = element.querySelector('.btn-danger');
                if (acceptBtn) acceptBtn.disabled = false;
                if (rejectBtn) rejectBtn.disabled = false;
            }

            this.updateRevisionStats();
            this.addToHistory({
                type: 'undo_revision',
                detail: `撤销修订操作: ${fieldPath}`
            });

            this.showToastMessage(`已撤销修订操作: ${fieldPath}`, 'info');
        },

        // 接受所有修订
        acceptAllRevisions() {
            const revisionElements = document.querySelectorAll('.revision-item[data-revision-type]:not([data-revision-type="uptodate"])');
            let count = 0;

            revisionElements.forEach(element => {
                const fieldPath = element.dataset.fieldPath;
                if (fieldPath) {
                    this.storage.revisions.set(fieldPath, 'accepted');
                    element.classList.remove('revision-status-pending', 'revision-status-rejected');
                    element.classList.add('revision-status-accepted');
                    count++;
                }
            });

            this.updateRevisionStats();
            this.addToHistory({
                type: 'accept_all_revisions',
                detail: `接受所有修订 (${count}个)`
            });

            this.showToastMessage(`已接受 ${count} 个修订`, 'success');
        },

        // 拒绝所有修订
        rejectAllRevisions() {
            const revisionElements = document.querySelectorAll('.revision-item[data-revision-type]:not([data-revision-type="uptodate"])');
            let count = 0;

            revisionElements.forEach(element => {
                const fieldPath = element.dataset.fieldPath;
                if (fieldPath) {
                    this.storage.revisions.set(fieldPath, 'rejected');
                    element.classList.remove('revision-status-pending', 'revision-status-accepted');
                    element.classList.add('revision-status-rejected');
                    count++;
                }
            });

            this.updateRevisionStats();
            this.addToHistory({
                type: 'reject_all_revisions',
                detail: `拒绝所有修订 (${count}个)`
            });

            this.showToastMessage(`已拒绝 ${count} 个修订`, 'warning');
        },

        // 应用修订过滤器
        applyRevisionFilter() {
            const elements = document.querySelectorAll('.revision-item');

            elements.forEach(element => {
                const type = element.dataset.revisionType;
                const status = element.dataset.status;

                let shouldShow = false;

                switch (this.revisionFilter) {
                    case 'all':
                        shouldShow = true;
                        break;
                    case 'pending':
                        shouldShow = status === 'pending';
                        break;
                    case 'accepted':
                        shouldShow = status === 'accepted';
                        break;
                    case 'rejected':
                        shouldShow = status === 'rejected';
                        break;
                    case 'uptodate':
                        shouldShow = type === 'uptodate';
                        break;
                }

                element.style.display = shouldShow ? 'block' : 'none';
            });

            // 隐藏没有可见子项的模块
            const modules = document.querySelectorAll('.revision-module');
            modules.forEach(module => {
                const visibleItems = module.querySelectorAll('.revision-item:not([style*="display: none"])');
                module.style.display = visibleItems.length > 0 ? 'block' : 'none';
            });
        },

        // 切换修订模块展开/折叠
        toggleRevisionModule(headerElement) {
            const moduleBody = headerElement.nextElementSibling;
            const arrow = headerElement.querySelector('.module-arrow');

            if (moduleBody.style.display === 'none') {
                moduleBody.style.display = 'block';
                arrow.textContent = '▲';
            } else {
                moduleBody.style.display = 'none';
                arrow.textContent = '▼';
            }
        },

        // 导出修订结果
        exportResult() {
            if (!this.targetData) {
                this.showToastMessage('没有目标数据可导出', 'warning');
                return;
            }

            try {
                // 应用所有接受的修订到目标数据
                const exportData = this.applyRevisionsToData(JSON.parse(JSON.stringify(this.targetData)));

                // 创建下载链接
                const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `revised-data-${new Date().toISOString().slice(0, 10)}.json`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);

                this.addToHistory({
                    type: 'export',
                    detail: '导出修订后的数据'
                });

                this.showToastMessage('数据导出成功', 'success');
            } catch (error) {
                this.handleError(error, '导出数据');
            }
        },

        // 应用修订到数据
        applyRevisionsToData(data) {
            const acceptedRevisions = new Map();

            // 收集所有接受的修订
            this.storage.revisions.forEach((status, fieldPath) => {
                if (status === 'accepted') {
                    acceptedRevisions.set(fieldPath, true);
                }
            });

            // 应用修订（这里需要根据实际的修订建议来实现）
            // 暂时返回原数据，实际实现需要根据修订建议修改数据
            return data;
        },

        // 过滤器功能
        setFilter(filter) {
            this.currentFilter = filter;
            this.applyFilter();
        },

        setRevisionFilter(filter) {
            this.revisionFilter = filter;
            this.applyRevisionFilter();
        },

        applyFilter() {
            // 调用原有的过滤逻辑
            if (typeof window.applyFilter === 'function') {
                window.applyFilter();
            }
        },

        applyRevisionFilter() {
            // 调用原有的修订过滤逻辑
            if (typeof window.applyRevisionFilter === 'function') {
                window.applyRevisionFilter();
            }
        },

        // 展开/折叠功能
        expandAllModules() {
            if (typeof window.expandAllModules === 'function') {
                window.expandAllModules();
            }
        },

        collapseAllModules() {
            if (typeof window.collapseAllModules === 'function') {
                window.collapseAllModules();
            }
        },

        // 接受/拒绝所有更改
        acceptAllChanges() {
            if (typeof window.acceptAllChanges === 'function') {
                window.acceptAllChanges();
                this.addToHistory({
                    type: 'accept_all',
                    detail: '接受所有更改'
                });
            }
        },

        rejectAllChanges() {
            if (typeof window.rejectAllChanges === 'function') {
                window.rejectAllChanges();
                this.addToHistory({
                    type: 'reject_all',
                    detail: '拒绝所有更改'
                });
            }
        },

        // 接受/拒绝所有修订
        acceptAllRevisions() {
            if (typeof window.acceptAllRevisions === 'function') {
                window.acceptAllRevisions();
                this.updateRevisionStats();
                this.addToHistory({
                    type: 'accept_all_revisions',
                    detail: '接受所有修订'
                });
            }
        },

        rejectAllRevisions() {
            if (typeof window.rejectAllRevisions === 'function') {
                window.rejectAllRevisions();
                this.updateRevisionStats();
                this.addToHistory({
                    type: 'reject_all_revisions',
                    detail: '拒绝所有修订'
                });
            }
        },

        // 数据加载功能
        async loadSampleData() {
            try {
                this.showToastMessage('正在加载示例数据...', 'info');

                // 调用原有的加载逻辑
                if (typeof window.loadSampleData === 'function') {
                    await window.loadSampleData();
                    // 同步数据到Alpine.js状态
                    this.leftData = window.leftData;
                    this.rightData = window.rightData;
                    this.targetData = window.targetData;

                    this.addToHistory({
                        type: 'data_load',
                        detail: '加载示例数据'
                    });
                }
            } catch (error) {
                this.showToastMessage('加载数据失败: ' + error.message, 'error');
            }
        },

        // 文件上传功能
        uploadFiles() {
            if (typeof window.uploadFiles === 'function') {
                window.uploadFiles();
            }
        },

        // 导出功能
        exportResult() {
            if (typeof window.exportResult === 'function') {
                window.exportResult();
                this.addToHistory({
                    type: 'export',
                    detail: '导出结果'
                });
            }
        },

        // 调试功能
        debugData() {
            const debugInfo = {
                leftData: this.leftData,
                rightData: this.rightData,
                targetData: this.targetData,
                currentMode: this.currentMode,
                diffStats: this.diffStats,
                revisionStats: this.revisionStats,
                storage: this.storage
            };

            console.log('调试数据:', debugInfo);
            this.showToastMessage('调试信息已输出到控制台', 'info');

            this.addToHistory({
                type: 'debug',
                detail: '查看调试数据'
            });
        },

        // 历史记录功能
        showHistory() {
            const history = this.storage.operationHistory;

            if (history.length === 0) {
                this.showModalDialog('操作历史', `
                    <div class="history-empty">
                        <div class="empty-state-icon">📜</div>
                        <div class="empty-state-text">暂无操作历史</div>
                    </div>
                `);
                return;
            }

            const content = `
                <div class="history-container">
                    ${history.map(item => `
                        <div class="history-item">
                            <div class="history-info">
                                <div class="history-type">
                                    <span class="history-type-icon">${this.getOperationIcon(item.type)}</span>
                                    ${this.getOperationTypeName(item.type)}
                                </div>
                                <div class="history-detail">${item.detail || ''}</div>
                            </div>
                            <div class="history-time">${this.formatTime(item.timestamp)}</div>
                        </div>
                    `).join('')}
                </div>
                <div style="margin-top: 20px; text-align: right;">
                    <button class="btn btn-secondary" onclick="$data.clearHistory()">清空历史</button>
                    <button class="btn btn-primary" onclick="$data.showModal = false">关闭</button>
                </div>
            `;

            this.showModalDialog('操作历史', content);
        },

        clearHistory() {
            if (confirm('确定要清空所有操作历史吗？')) {
                this.storage.operationHistory = [];
                this.showModal = false;
                this.showToastMessage('操作历史已清空', 'success');
            }
        },

        // 版本管理相关方法
        loadVersion(index) {
            const version = this.storage.versions[index];
            if (version && version.data) {
                this.leftData = version.data.left;
                this.rightData = version.data.right;
                this.targetData = version.data.target;

                // 同步到全局变量（兼容性）
                window.leftData = this.leftData;
                window.rightData = this.rightData;
                window.targetData = this.targetData;

                this.showModal = false;
                this.showToastMessage(`已加载${version.name}`, 'success');

                // 重新对比数据
                if (this.currentMode === 'revision' && this.targetData) {
                    this.showRevisionMode();
                } else {
                    this.compareData();
                }

                this.addToHistory({
                    type: 'version_load',
                    detail: `加载${version.name}`
                });
            }
        },

        previewVersion(index) {
            const version = this.storage.versions[index];
            if (!version) return;

            const content = `
                <div class="version-preview">
                    <div class="version-preview-section">
                        <h4>版本信息</h4>
                        <p><strong>名称:</strong> ${version.name || `版本 ${version.id}`}</p>
                        <p><strong>时间:</strong> ${this.formatTime(version.timestamp)}</p>
                    </div>

                    ${version.data.left ? `
                        <div class="version-preview-section">
                            <h4>版本1数据预览</h4>
                            <pre class="json-preview">${JSON.stringify(Object.entries(version.data.left).slice(0, 3).reduce((obj, [k, v]) => ({...obj, [k]: v}), {}), null, 2)}</pre>
                        </div>
                    ` : ''}

                    ${version.data.right ? `
                        <div class="version-preview-section">
                            <h4>版本2数据预览</h4>
                            <pre class="json-preview">${JSON.stringify(Object.entries(version.data.right).slice(0, 3).reduce((obj, [k, v]) => ({...obj, [k]: v}), {}), null, 2)}</pre>
                        </div>
                    ` : ''}

                    ${version.data.target ? `
                        <div class="version-preview-section">
                            <h4>版本3数据预览</h4>
                            <pre class="json-preview">${JSON.stringify(Object.entries(version.data.target).slice(0, 3).reduce((obj, [k, v]) => ({...obj, [k]: v}), {}), null, 2)}</pre>
                        </div>
                    ` : ''}

                    <div style="margin-top: 20px; text-align: right;">
                        <button class="btn btn-primary" onclick="$data.loadVersion(${index})">加载此版本</button>
                        <button class="btn btn-secondary" onclick="$data.showVersionHistory()">返回</button>
                    </div>
                </div>
            `;

            this.showModalDialog(`版本预览 - ${version.name || `版本 ${version.id}`}`, content);
        },

        // 模块配置相关方法
        toggleModuleConfig(element) {
            const body = element.nextElementSibling;
            const arrow = element.querySelector('.module-config-arrow');

            if (body.style.display === 'none') {
                body.style.display = 'block';
                arrow.textContent = '▲';
            } else {
                body.style.display = 'none';
                arrow.textContent = '▼';
            }
        },

        addModuleIndexField(moduleName) {
            const input = document.querySelector(`[data-module="${moduleName}"] .tag-input input`);
            if (input && input.value.trim()) {
                const field = input.value.trim();

                // 更新配置
                if (!this.storage.moduleIndexConfig[moduleName]) {
                    this.storage.moduleIndexConfig[moduleName] = [];
                }

                if (!this.storage.moduleIndexConfig[moduleName].includes(field)) {
                    this.storage.moduleIndexConfig[moduleName].push(field);
                    this.renderModuleConfigs();
                    this.showToastMessage(`已添加索引字段: ${field}`, 'success');
                }

                input.value = '';
            }
        },

        removeModuleIndexField(moduleName, field) {
            if (this.storage.moduleIndexConfig[moduleName]) {
                const index = this.storage.moduleIndexConfig[moduleName].indexOf(field);
                if (index > -1) {
                    this.storage.moduleIndexConfig[moduleName].splice(index, 1);
                    this.renderModuleConfigs();
                    this.showToastMessage(`已移除索引字段: ${field}`, 'info');
                }
            }
        },

        showVersionHistory() {
            const versions = this.storage.versions;

            if (versions.length === 0) {
                this.showModalDialog('版本历史', `
                    <div class="history-empty">
                        <div class="empty-state-icon">📋</div>
                        <div class="empty-state-text">暂无版本历史</div>
                    </div>
                `);
                return;
            }

            const content = `
                <div class="version-list">
                    ${versions.map((version, index) => `
                        <div class="version-item">
                            <div class="version-info">
                                <div class="version-name">${version.name || `版本 ${version.id}`}</div>
                                <div class="version-time">${this.formatTime(version.timestamp)}</div>
                            </div>
                            <div class="version-details">
                                <span>版本1: ${version.data.left ? Object.keys(version.data.left).length : 0} 个模块</span>
                                <span>版本2: ${version.data.right ? Object.keys(version.data.right).length : 0} 个模块</span>
                                ${version.data.target ? `<span>版本3: ${Object.keys(version.data.target).length} 个模块</span>` : ''}
                            </div>
                            <div class="version-actions">
                                <button class="btn btn-primary" onclick="$data.loadVersion(${index})">
                                    <span>📂</span> 加载此版本
                                </button>
                                <button class="btn btn-secondary" onclick="$data.previewVersion(${index})">
                                    <span>👁️</span> 预览
                                </button>
                            </div>
                        </div>
                    `).join('')}
                </div>
                <div style="margin-top: 20px; text-align: right;">
                    <button class="btn btn-primary" onclick="$data.showModal = false">关闭</button>
                </div>
            `;

            this.showModalDialog('版本历史', content);
        },

        // 工具方法
        getOperationIcon(type) {
            const icons = {
                'init': '🚀',
                'mode_switch': '🔄',
                'data_input': '📝',
                'data_load': '📁',
                'file_upload': '⬆️',
                'accept_all': '✅',
                'reject_all': '❌',
                'accept_all_revisions': '✅',
                'reject_all_revisions': '❌',
                'export': '💾',
                'debug': '🐛'
            };
            return icons[type] || '📋';
        },

        getOperationTypeName(type) {
            const names = {
                'init': '初始化',
                'mode_switch': '模式切换',
                'data_input': '数据输入',
                'data_load': '数据加载',
                'file_upload': '文件上传',
                'accept_all': '接受所有更改',
                'reject_all': '拒绝所有更改',
                'accept_all_revisions': '接受所有修订',
                'reject_all_revisions': '拒绝所有修订',
                'export': '导出结果',
                'debug': '调试数据'
            };
            return names[type] || '未知操作';
        },

        formatTime(timestamp) {
            return new Date(timestamp).toLocaleString('zh-CN');
        },

        // 文件上传处理
        handleFileUpload(event) {
            const files = Array.from(event.target.files);
            if (files.length < 2 || files.length > 3) {
                this.showToastMessage('请选择2个或3个JSON文件进行对比', 'warning');
                return;
            }

            const readers = files.map(file => {
                return new Promise((resolve, reject) => {
                    const reader = new FileReader();
                    reader.onload = (e) => {
                        try {
                            const data = JSON.parse(e.target.result);
                            resolve(data);
                        } catch (error) {
                            reject(error);
                        }
                    };
                    reader.onerror = reject;
                    reader.readAsText(file);
                });
            });

            Promise.all(readers)
                .then((results) => {
                    this.leftData = results[0];
                    this.rightData = results[1];
                    this.targetData = results[2] || null;

                    // 同步到全局变量（兼容性）
                    window.leftData = this.leftData;
                    window.rightData = this.rightData;
                    window.targetData = this.targetData;

                    // 清空修订状态
                    this.clearRevisions();
                    this.clearChangeStatus();
                    this.storage.dataVersion++;

                    // 保存版本
                    this.addVersion({
                        left: this.leftData,
                        right: this.rightData,
                        target: this.targetData
                    });

                    // 记录操作历史
                    this.addToHistory({
                        type: 'file_upload',
                        detail: `上传${files.length}个文件`
                    });

                    this.compareData();
                    this.showToastMessage('文件上传成功', 'success');
                })
                .catch(error => {
                    this.showToastMessage('文件解析失败: ' + error.message, 'error');
                });
        },

        // 创建文件输入元素并触发上传
        triggerFileUpload() {
            const input = document.createElement('input');
            input.type = 'file';
            input.multiple = true;
            input.accept = '.json';
            input.onchange = (event) => this.handleFileUpload(event);
            input.click();
        },

        // 高级过滤功能
        applyAdvancedFilter() {
            // 这里可以实现更复杂的过滤逻辑
            this.applyFilter();
        },

        // 搜索功能
        searchInContent(searchTerm) {
            if (!searchTerm.trim()) {
                this.applyFilter();
                return;
            }

            // 实现搜索逻辑
            const elements = document.querySelectorAll('.diff-item, .revision-item');
            elements.forEach(element => {
                const text = element.textContent.toLowerCase();
                const shouldShow = text.includes(searchTerm.toLowerCase());
                element.style.display = shouldShow ? 'block' : 'none';
            });
        },

        // 键盘快捷键处理
        handleKeyboardShortcut(event) {
            if (event.ctrlKey || event.metaKey) {
                switch (event.key) {
                    case 'a':
                        event.preventDefault();
                        if (this.currentMode === 'revision') {
                            this.acceptAllRevisions();
                        } else {
                            this.acceptAllChanges();
                        }
                        break;
                    case 'r':
                        event.preventDefault();
                        if (this.currentMode === 'revision') {
                            this.rejectAllRevisions();
                        } else {
                            this.rejectAllChanges();
                        }
                        break;
                    case 's':
                        event.preventDefault();
                        if (this.currentMode === 'revision') {
                            this.exportResult();
                        }
                        break;
                }
            } else if (event.key === 'Tab') {
                event.preventDefault();
                this.switchMode(this.currentMode === 'diff' ? 'revision' : 'diff');
            } else if (event.key === 'Escape') {
                // 关闭模态框
                this.showModal = false;
                this.showJsonInputModal = false;
            }
        },

        // 数据验证
        validateJsonData(data, version) {
            if (!data || typeof data !== 'object') {
                throw new Error(`版本${version}数据格式无效`);
            }

            if (Object.keys(data).length === 0) {
                throw new Error(`版本${version}数据为空`);
            }

            return true;
        },

        // 性能监控
        measurePerformance(operation, fn) {
            const start = performance.now();
            const result = fn();
            const end = performance.now();

            console.log(`${operation} 耗时: ${(end - start).toFixed(2)}ms`);

            if (end - start > 1000) {
                this.showToastMessage(`${operation}耗时较长，请稍候...`, 'warning');
            }

            return result;
        },

        // 错误处理
        handleError(error, context = '') {
            console.error(`错误 ${context}:`, error);
            this.showToastMessage(`${context}发生错误: ${error.message}`, 'error');

            this.addToHistory({
                type: 'error',
                detail: `${context}: ${error.message}`
            });
        }
    };
}

// 注册Alpine.js数据组件
document.addEventListener('alpine:init', () => {
    Alpine.data('diffApp', diffApp);
});
